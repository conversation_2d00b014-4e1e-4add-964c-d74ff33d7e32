serverAddr = "***************"
serverPort = 7000
loginFailExit = false
start = [
    "vlcplayer", "web_tcp_8820", "webhoo", "PVE", "mt5", "12", "vlc", "Guacamole", "FRPC", "vnc"
]

[auth]
method = "token"
token = "60d8a83c544e6168db"

[webServer]
addr = "0.0.0.0"
port = 7400
user = "frpc_user"
password = "3TSzKW56jH6hxbAz"
pprofEnable = false

[frpmgr]
name = "vlc"

[log]
level = "info"
maxDays = 3
to = "C:/Program Files/FRP/logs/313c40d6f4a5b8b40085d8f6cca40d5d.log"

[[proxies]]
customDomains = ["eng.mios.fun"]
localIP = "*************"
localPort = 5000
name = "vlcplayer"
type = "http"

[[proxies]]
customDomains = ["dd1.mios.fun"]
localIP = "*************"
localPort = 8080
name = "web_tcp_8820"
type = "http"

[[proxies]]
customDomains = ["dd2.mios.fun"]
localIP = "*************"
localPort = 9999
name = "webhoo"
type = "http"

[[proxies]]
customDomains = ["dd3.mios.fun"]
name = "PVE"
type = "https"

[proxies.metadatas]
plugin_host_header_rewrite = ""

[proxies.plugin]
enableHTTP2 = true
localAddr = "*************:8006"
type = "https2https"

[[proxies]]
customDomains = ["dd5.mios.fun"]
localIP = "*************"
localPort = 3000
name = "mt5"
type = "http"

[[proxies]]
localIP = "*************"
localPort = 8006
name = "12"
remotePort = 48006
type = "tcp"

[[proxies]]
customDomains = ["dd4.mios.fun"]
localIP = "*************"
localPort = 8080
name = "Guacamole"
type = "http"

[[proxies]]
localIP = "*************"
localPort = 4000
name = "vlc"
remotePort = 40801
type = "tcp"

[[proxies]]
customDomains = ["dd8.mios.fun"]
localIP = "*************"
localPort = 7400
name = "FRPC"
type = "http"

[[proxies]]
customDomains = ["dd9.mios.fun"]
localIP = "*************"
localPort = 8444
name = "vnc"
type = "http"

[transport]
tcpMux = true

[transport.tls]
disableCustomTLSFirstByte = true
enable = true
